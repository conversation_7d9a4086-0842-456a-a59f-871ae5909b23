// 统一的 LOGO 图标定义 - 使用你的原始 icon.svg 内容
export const LogoIcon = ({ className = "size-10" }: { className?: string }) => (
	<svg
		className={className}
		width="512"
		height="512"
		version="1.1"
		xmlns="http://www.w3.org/2000/svg"
		viewBox="0 0 512 512"
	>
		<title>StarlinkLogo</title>
		<path fill="rgb(0,0,0)" stroke="rgb(0,0,0)" strokeWidth="1" opacity="0" d="M 0 0 L 512 0 L 512 512 L 0 512 L 0 0 Z M 320 99 L 316 101 L 312 106 L 312 172 L 168 172 L 156 178 Q 147 184 144 196 L 144 221 L 117 221 L 107 226 Q 101 231 99 239 L 99 300 L 104 310 Q 109 315 117 317 L 144 317 L 144 351 L 149 362 Q 155 371 168 374 L 173 375 L 194 375 L 194 397 L 197 404 Q 201 411 211 413 Q 221 415 227 411 L 270 375 L 341 375 L 349 373 L 356 369 Q 365 363 368 351 L 368 317 L 396 317 L 406 312 Q 411 307 413 300 L 413 239 L 408 229 Q 403 223 396 221 L 368 221 L 368 196 L 362 184 Q 357 176 348 173 L 335 172 L 335 108 L 333 104 Q 330 97 320 99 Z " />
		<path fill="rgb(0,0,0)" stroke="rgb(0,0,0)" strokeWidth="1" opacity="0" d="M 172.5 195 L 339.5 195 L 343 197 L 345 200.5 L 345 345.5 L 341.5 350 L 339.5 351 L 266.5 351 L 262.5 352 L 255.5 356 L 217.5 388 L 217 367.5 Q 214.5 359.5 208.5 355 L 198.5 351 L 172.5 351 L 168 347.5 L 167 345.5 L 167 201.5 L 169 197 L 172.5 195 Z M 198 239 L 191 242 L 188 249 Q 187 255 190 258 L 196 262 L 317 262 L 319 261 Q 323 258 324 253 Q 324 245 321 242 L 315 239 L 198 239 Z M 196 288 L 194 289 Q 189 292 188 298 L 189 305 Q 191 310 198 311 L 282 311 L 286 309 L 290 303 L 289 294 L 283 288 L 196 288 Z " />
		<path fill="rgb(0,0,0)" stroke="rgb(0,0,0)" strokeWidth="1" opacity="0" d="M 122 244 L 144 244 L 144 294 L 122 294 L 122 244 Z " />
		<path fill="rgb(0,0,0)" stroke="rgb(0,0,0)" strokeWidth="1" opacity="0" d="M 368 244 L 390 244 L 390 294 L 368 294 L 368 244 Z " />
		<path fill="rgb(252,215,0)" stroke="rgb(252,215,0)" strokeWidth="1" opacity="0.9607843137254902" d="M 319.5 99 Q 330.3 97.2 333 103.5 L 335 107.5 L 335 172 L 347.5 173 Q 356.5 176.5 362 183.5 L 368 195.5 L 368 221 L 395.5 221 Q 403.3 223.3 408 228.5 L 413 238.5 L 413 299.5 Q 410.8 307.3 405.5 312 L 395.5 317 L 368 317 L 368 350.5 Q 364.7 362.7 355.5 369 L 348.5 373 L 340.5 375 L 269.5 375 L 226.5 411 Q 221.1 414.6 210.5 413 Q 201.4 410.5 197 403.5 L 194 396.5 L 194 375 L 172.5 375 L 167.5 374 Q 155.3 370.7 149 361.5 L 144 350.5 L 144 317 L 116.5 317 Q 108.8 314.8 104 309.5 L 99 299.5 L 99 238.5 Q 101.3 230.8 106.5 226 L 116.5 221 L 144 221 L 144 195.5 Q 147.4 184.4 155.5 178 L 167.5 172 L 312 172 L 312 105.5 L 315.5 101 L 319.5 99 Z M 173 195 L 169 197 L 167 202 L 167 346 L 168 348 L 173 351 L 199 351 L 209 355 Q 214 359 217 368 L 218 388 L 256 356 L 263 352 L 267 351 L 340 351 L 342 350 L 345 346 L 345 201 L 343 197 L 340 195 L 173 195 Z M 122 244 L 122 294 L 144 294 L 144 244 L 122 244 Z M 368 244 L 368 294 L 390 294 L 390 244 L 368 244 Z " />
		<path fill="rgb(252,215,0)" stroke="rgb(252,215,0)" strokeWidth="1" opacity="0.9607843137254902" d="M 197.5 239 L 314.5 239 L 321 242 Q 324 245.2 324 252.5 Q 322.8 258.3 318.5 261 L 316.5 262 L 195.5 262 L 190 257.5 Q 187.4 254.6 188 248.5 L 191 242 L 197.5 239 Z " />
		<path fill="rgb(252,215,0)" stroke="rgb(252,215,0)" strokeWidth="1" opacity="0.9607843137254902" d="M 195.5 288 L 282.5 288 L 289 293.5 L 290 302.5 L 285.5 309 L 281.5 311 L 197.5 311 Q 191.4 309.6 189 304.5 L 188 297.5 Q 189.3 291.8 193.5 289 L 195.5 288 Z " />
	</svg>
);

