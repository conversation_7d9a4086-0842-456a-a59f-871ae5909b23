import type { Metadata } from "next";
import type { PropsWithChildren } from "react";
import "./globals.css";
import "cropperjs/dist/cropper.css";

export const metadata: Metadata = {
	/**
	 * 网站基础URL配置 - 用于生成绝对URL
	 * 
	 * 作用：
	 * - 为相对路径的metadata字段提供基础URL
	 * - 确保Open Graph、Twitter卡片等使用完整URL
	 * - 支持多环境配置（开发、测试、生产）
	 * 
	 * 修改时间: 2025-07-05
	 * 修改原因: 添加基础URL配置以支持完整的SEO优化
	 */
	metadataBase: new URL('https://starlink.com'),

	/**
	 * 网站标题配置 - 控制浏览器标签页标题和SEO标题显示
	 * 
	 * 工作原理：
	 * - Next.js 会根据页面层级和配置自动生成最终标题
	 * - 子页面可以通过 metadata.title 覆盖或扩展这些配置
	 * 
	 * 配置说明：
	 * - absolute: 绝对标题，当页面设置 absolute 时直接使用，不会应用 template
	 * - default: 默认标题，当页面没有设置 title 时使用
	 * - template: 标题模板，%s 占位符会被页面标题替换
	 * 
	 * 示例效果：
	 * - 首页：显示 "星链" (使用 default)
	 * - 登录页面设置 title: "登录"：显示 "登录 | 星链" (使用 template)
	 * - 某页面设置 title: { absolute: "独立标题" }：显示 "独立标题" (使用 absolute)
	 * 
	 * 修改时间: 2025-07-05
	 * 修改原因: 添加详细注释说明 title 配置的作用和工作原理
	 */
	title: {
		absolute: "星链",        // 绝对标题 - 当页面明确指定绝对标题时使用
		default: "星链",         // 默认标题 - 当页面没有指定标题时的后备标题
		template: "%s | 星链",   // 标题模板 - %s 会被页面标题替换，形成"页面标题 | 星链"格式
	},
	description: "星链资本 - 专业的市值管理AI平台",
	
	/**
	 * 关键词配置 - 帮助搜索引擎理解网站内容
	 * 
	 * 作用：
	 * - 提供网站核心关键词信息
	 * - 帮助搜索引擎分类和索引网站内容
	 * - 影响搜索结果的相关性匹配
	 * 
	 * 修改时间: 2025-07-05
	 * 修改原因: 添加关键词配置以提升SEO效果
	 */
	keywords: ['星链', '市值管理', 'AI平台', '资本管理', '投资', '金融科技', '智能投资', '股权管理'],
	
	/**
	 * 网站分类配置 - 定义网站所属行业类别
	 * 
	 * 修改时间: 2025-07-05
	 * 修改原因: 添加分类信息以便搜索引擎更好地理解网站定位
	 */
	category: '金融科技',
	
	/**
	 * 应用程序配置 - 定义应用程序相关信息
	 * 
	 * 作用：
	 * - 设置应用程序名称，用于添加到主屏幕等场景
	 * - 配置Apple设备的Web应用体验
	 * - 提升移动端用户体验
	 * 
	 * 修改时间: 2025-07-05
	 * 修改原因: 添加应用程序配置以提升移动端体验
	 */
	applicationName: '星链',
	appleWebApp: {
		capable: true,
		title: '星链',
		statusBarStyle: 'black-translucent',
	},
	
	/**
	 * 搜索引擎爬虫配置 - 控制搜索引擎如何索引网站
	 * 
	 * 配置说明：
	 * - index: 允许搜索引擎索引页面
	 * - follow: 允许搜索引擎跟踪页面链接
	 * - googleBot: 专门针对Google爬虫的配置
	 * - max-video-preview: 允许视频预览的最大长度
	 * - max-image-preview: 允许图片预览的最大尺寸
	 * - max-snippet: 允许文本片段的最大长度
	 * 
	 * 修改时间: 2025-07-05
	 * 修改原因: 添加搜索引擎爬虫配置以优化SEO表现
	 */
	robots: {
		index: true,
		follow: true,
		googleBot: {
			index: true,
			follow: true,
			'max-video-preview': -1,
			'max-image-preview': 'large',
			'max-snippet': -1,
		},
	},
	
	/**
	 * Open Graph 社交媒体优化配置 - 控制社交媒体分享时的展示效果
	 * 
	 * 作用：
	 * - 优化在Facebook、LinkedIn等平台的分享展示
	 * - 提供丰富的分享卡片信息
	 * - 提升社交媒体传播效果
	 * 
	 * 配置说明：
	 * - type: 网站类型
	 * - locale: 语言和地区
	 * - siteName: 网站名称
	 * - images: 分享时显示的图片
	 * 
	 * 修改时间: 2025-07-05
	 * 修改原因: 添加Open Graph配置以优化社交媒体分享效果
	 */
	openGraph: {
		type: 'website',
		locale: 'zh_CN',
		url: 'https://starlink.com',
		siteName: '星链',
		title: '星链 - 专业的市值管理AI平台',
		description: '星链资本 - 专业的市值管理AI平台，为投资者提供智能化的市值管理解决方案',
		images: [
			{
				url: '/og-image.jpg',
				width: 1200,
				height: 630,
				alt: '星链平台预览图',
			},
		],
	},
	
	/**
	 * Twitter 卡片配置 - 控制Twitter分享时的展示效果
	 * 
	 * 作用：
	 * - 优化在Twitter平台的分享展示
	 * - 提供Twitter专用的卡片信息
	 * - 提升Twitter传播效果
	 * 
	 * 配置说明：
	 * - card: 卡片类型，summary_large_image为大图卡片
	 * - images: Twitter分享时显示的图片
	 * 
	 * 修改时间: 2025-07-05
	 * 修改原因: 添加Twitter卡片配置以优化Twitter分享效果
	 */
	twitter: {
		card: 'summary_large_image',
		title: '星链 - 专业的市值管理AI平台',
		description: '星链资本 - 专业的市值管理AI平台，为投资者提供智能化的市值管理解决方案',
		images: ['/twitter-image.jpg'],
	},
	
	/**
	 * 多语言和地区配置 - 支持国际化和本地化
	 * 
	 * 作用：
	 * - 告知搜索引擎网站的多语言版本
	 * - 设置规范URL避免重复内容
	 * - 支持不同地区的用户访问
	 * 
	 * 配置说明：
	 * - canonical: 规范URL，避免重复内容问题
	 * - languages: 不同语言版本的URL
	 * 
	 * 修改时间: 2025-07-05
	 * 修改原因: 添加多语言配置以支持国际化需求
	 */
	alternates: {
		canonical: 'https://starlink.com',
		languages: {
			'zh-CN': 'https://starlink.com/zh',
			'en-US': 'https://starlink.com/en',
		},
	},
	
	/**
	 * 网站验证配置 - 用于各大搜索引擎和平台的网站验证
	 * 
	 * 作用：
	 * - 验证网站所有权
	 * - 启用搜索引擎控制台功能
	 * - 接入各种分析和优化工具
	 * 
	 * 注意：实际使用时需要替换为真实的验证码
	 * 
	 * 修改时间: 2025-07-05
	 * 修改原因: 添加验证配置以便接入各种SEO和分析工具
	 */
	verification: {
		google: 'your-google-verification-code',
		other: {
			'baidu-site-verification': 'your-baidu-verification-code',
		},
	},
	
	/**
	 * 网站图标配置 - 控制浏览器标签页图标、收藏夹图标等
	 *
	 * Next.js 13+ App Router 图标约定：
	 * - icon.svg/icon.png: 自动作为网站图标
	 * - favicon.ico: 传统 favicon 文件
	 * - apple-icon.png: Apple 设备专用图标
	 *
	 * 配置说明：
	 * - icon: 主要图标，支持多种格式和尺寸
	 * - shortcut: 快捷方式图标，通常是 favicon.ico
	 * - apple: Apple 设备图标
	 */
	icons: {
		icon: [
			{ url: "/icon.svg", type: "image/svg+xml" },
			{ url: "/icon.png", type: "image/png", sizes: "512x512" },
		],
		shortcut: "/favicon.ico",
		apple: "/icon.png",
	},
};

export default function RootLayout({ children }: PropsWithChildren) {
	return children;
}
